module AgentPortal
  class ApplicationController < ::ActionController::Base
    include Authentication
    include Pagy::Backend
    layout "agent_portal"

    before_action :check_default_password

    private

    def check_default_password
      return unless Current.agent

      if Current.agent.using_default_password?
        redirect_to agent_portal_force_password_change_path
      end
    end
  end
end
