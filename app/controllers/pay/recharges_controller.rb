class Pay::RechargesController < ApplicationController
  # 充值订单
  # GET /pay/recharges
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = Recharge.left_joins(:user, :channel, :agent).select("recharges.*, users.nickname,users.email_address as email,users.mobile,users.name,channels.name as channel_name,channels.nickname as channel_nickname,agents.name as agent_name,agents.nickname as agent_nickname").order(id: :desc)
    query = query.where("user_id = ? or username = ?", "#{params[:search]}", "#{params[:search]}") if params[:search].present?
    query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?

    # 处理首充过滤
    if params[:first_recharge].present?
      case params[:first_recharge]
      when "true"
        query = query.where(first_recharge: true)
      when "false"
        query = query.where(first_recharge: false)
      end
    end

    # 处理时间范围过滤
    begin
      if params[:start_time].present? && params[:end_time].present?
        start_time = Date.parse(params[:start_time]).beginning_of_day
        end_time = Date.parse(params[:end_time]).end_of_day
        query = query.where(created_at: start_time..end_time)
      elsif params[:start_time].present?
        start_time = Date.parse(params[:start_time]).beginning_of_day
        query = query.where("created_at >= ?", start_time)
      elsif params[:end_time].present?
        end_time = Date.parse(params[:end_time]).end_of_day
        query = query.where("created_at <= ?", end_time)
      end
    rescue Date::Error => e
      Rails.logger.warn "日期解析失败: #{e.message}, start_time: #{params[:start_time]}, end_time: #{params[:end_time]}"
      # 忽略无效的日期参数，继续执行查询
    end

    query = query.where(status: params[:status].to_i) if params[:status].present?


    @pagy, @recharges = pagy(query, limit: size, page: current)
  end
end
