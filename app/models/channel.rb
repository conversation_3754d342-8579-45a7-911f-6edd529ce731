class Channel < ApplicationRecord
  include LoginAble
  belongs_to :agent
  belongs_to :promotion_type

  DEFAULT_PASSWORD = "ZYSay3498ecsk8#2"

  validates :timezone, presence: true
  validates :remark, length: { maximum: 50 }

  enum :status, [ :actived, :inactived, :deleted ], default: :actived
  def status_text
    I18n.t("common.status.#{status}")
  end

  # 检查是否使用默认密码
  def using_default_password?
    authenticate(DEFAULT_PASSWORD)
  end
end
