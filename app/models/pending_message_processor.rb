class PendingMessageProcessor
  # 这个类现在主要作为向后兼容的接口
  # 实际的pending消息处理逻辑已经集成到RedisStreamProcessor中

  def self.process_pending_messages(stream_key: nil, group_name: nil)
    Rails.logger.info "PendingMessageProcessor.process_pending_messages called - delegating to RedisStreamProcessor"

    # 创建一个临时的RedisStreamProcessor实例来处理pending消息
    processor = RedisStreamProcessor.new
    processor.send(:process_pending_messages)

    Rails.logger.info "Pending message processing completed"
    true
  end
end
