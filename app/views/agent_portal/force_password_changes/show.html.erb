<div class="w-full max-w-md">
  <div class="bg-white rounded-lg shadow-xl p-8">
    <div class="text-center">
      <h2 class="text-2xl font-bold text-gray-900">
        安全提醒
      </h2>
      <p class="mt-2 text-sm text-gray-600">
        检测到您正在使用默认密码，为了账户安全，请立即修改密码
      </p>
    </div>
    
    <div class="mt-6 bg-yellow-50 border-l-4 border-yellow-400 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">
            安全警告
          </h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>使用默认密码存在安全风险，请立即设置一个新的安全密码。</p>
          </div>
        </div>
      </div>
    </div>

    <%= form_with(model: @agent, url: agent_portal_force_password_change_path, method: :patch, class: "mt-8 space-y-6", data: { turbo: false }) do |f| %>
      <%= render "shared/error_messages", object: @agent %>
      
      <div class="space-y-4">
        <div>
          <%= f.label :password, "新密码", class: "block text-sm font-medium text-gray-700" %>
          <%= f.password_field :password, 
              required: true, 
              class: "input",
              placeholder: "请输入新密码（8-32个字符）" %>
          <p class="mt-1 text-xs text-gray-500">
            密码长度为8-32个字符，不能使用默认密码
          </p>
        </div>

        <div>
          <%= f.label :password_confirmation, "确认新密码", class: "block text-sm font-medium text-gray-700" %>
          <%= f.password_field :password_confirmation, 
              required: true, 
              class: "input",
              placeholder: "请再次输入新密码" %>
        </div>
      </div>

      <div>
        <%= f.submit "修改密码", class: "btn btn-primary" %>
      </div>
    <% end %>
    
    <div class="mt-6 text-center">
      <p class="text-xs text-gray-500">
        修改密码后，您将可以正常使用系统的所有功能
      </p>
    </div>
  </div>
</div>
