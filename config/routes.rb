require "sidekiq/web"
require "sidekiq/cron/web"

Rails.application.routes.draw do
  # TODO: 线上环境需要限制直接对 sidekiq 的 web 界面访问
  mount Sidekiq::Web => "/sidekiq"

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  get "dashboard" => "dashboard#index" # 后台首页
  get "dashboard/period_report" => "dashboard#period_report" # 时段报表

  # Defines the root path route ("/")
  root "root#index"
  resource :session, only: [ :new, :create, :destroy ]
  resources :registrations, only: [ :new, :create ]
  resources :passwords, param: :token, only: [ :new, :create, :edit, :update ] do
    collection do
      get :change
      patch :update_password
    end
  end
  # 代理商后台
  namespace :agent_portal do
    resource :session, only: [ :new, :create, :destroy ]
    resource :force_password_change, only: [ :show, :update ]
    root "dashboard#index"
    resources :channels do
      member do
        post :reset_password
      end
    end
    namespace :profile do
      resource :setting, only: [ :show, :update ]
      resource :password, only: [ :show, :update ]
    end
  end
  # 渠道后台
  namespace :channel_portal do
    resource :session, only: [ :new, :create, :destroy ]
    resource :force_password_change, only: [ :show, :update ]
    root "dashboard#index"
    namespace :profile do
      resource :setting, only: [ :show, :update ]
    end
  end

  # 系统管理
  namespace :system do
    resources :setting_items
    resources :redis_streams, only: [ :index ] do
      collection do
        delete :clear_stream
      end
    end
    get "system_info", to: "dashboard#system_info"
  end

  # 风控管理
  namespace :risk_manage do
    resources :black_ips, except: [ :show, :edit, :update ]
    resources :black_bank_cards, except: [ :show, :edit, :update ]
    resources :blacklist_records, except: [ :show, :edit, :update ]
  end

  # 后台管理
  namespace :back_manage do
    resources :admins do
      member do
        post :reset_password
      end
    end
    resources :roles
    resources :menus
    resources :system_logs, only: [ :index ]
  end

  # 运营管理
  namespace :campaign do
    resources :agents do # 代理商
      member do
        post :reset_password
      end
    end
    resources :promotion_types do
      member do
        put :toggle_status
      end
    end
    resources :channels do # 渠道
      member do
        post :reset_password
      end
    end
    resources :hall_ads # 大厅广告
    resources :popup_ads # 弹窗广告
    resources :floating_ads # 悬浮入口广告
    resource :setting, only: [ :show, :update ] # 全局设置
  end

  namespace :game do
    resources :game_types # 游戏类型管理
    resources :game_platforms # 游戏平台管理
    resources :integrators # 集成商管理
    resources :games # 子游戏管理
  end
  namespace :profile do
    resource :setting, only: [ :show, :update ]
    resource :password, only: [ :show, :update ]
  end
  namespace :pay do
    resources :payments
    resources :payment_types
    resource :payment_setting, only: [ :show, :update ]
    resources :recharges
    resources :withdraws
  end

  namespace :user do
    resources :users
    resources :tags
    resources :layers, only: [ :index ]
  end

  namespace :api do
    namespace :v1 do
      post "/auth/login", to: "auth#login", as: :auth_login
      get "/auth/user_info", to: "auth#user_info", as: :auth_user_info
      get "/route/user_routes", to: "route#user_routes", as: :route_user_routes
      get "/route/constant_routes", to: "route#constant_routes", as: :route_constant_routes
      get "/route/route_exist", to: "route#route_exist", as: :route_route_exist

      resources :roles, only: [ :index, :create, :update, :destroy ] do
        collection do
          get :all
        end
        member do
          get "menus", to: "roles#get_menus", as: :get_menus
          put "menus", to: "roles#update_menus", as: :update_menus
        end
      end
      resources :admins
      resources :agents
      resources :pages, only: [ :index ]
      resources :menus, only: [ :index, :update, :destroy ] do
        collection do
          delete :batch
        end
      end
      resources :menus do
        collection do
          get :tree
        end
      end
      resources :system_logs, only: [ :index ]
      namespace :user do
        resources :users, only: [ :index, :show, :create, :update, :destroy ]
      end

      resources :tags
      resources :users do
        resources :user_tags, only: [ :index, :create, :destroy ]
      end

      resources :black_ips
      resources :white_ips
      resources :black_bank_cards
      resources :recharges, only: [ :index, :show ]
      resources :withdraws, only: [ :index, :show, :update ]
    end
  end
end
