<%= render "agent_portal/profile/tab" %>

<div class="max-w-2xl mt-8 p-6 bg-white rounded-lg shadow-md">
  <h2 class="text-xl mb-6">安全设置</h2>

  <%= form_with(model: @agent, url: agent_portal_profile_password_path, method: :patch, class: "space-y-6", data: { turbo: false }) do |f| %>

    <div>
      <%= f.label :old_password, "原密码", class: "block text-sm font-medium text-gray-700" %>
      <% # check old password error exist %>
      <% if @agent.errors[:old_password].present? %>
        <%= f.password_field :old_password, required: true, class: "input border-red-500" %>
        <div class="text-red-500 text-sm">
          <%= @agent.errors.full_messages_for(:old_password).first %>
        </div>
      <% else %>
        <%= f.password_field :old_password, required: true, class: "input" %>
      <% end %>
    </div>

    <div>
      <%= f.label :password, "新密码", class: "block text-sm font-medium text-gray-700" %>
      <% if @agent.errors[:password].present? %>
        <%= f.password_field :password, class: "input border-red-500" %>
        <div class="text-red-500 text-sm">
          <%= @agent.errors.full_messages_for(:password).first %>
        </div>
      <% else %>
        <%= f.password_field :password, required: true, class: "input" %>
      <% end %>
    </div>

    <div>
      <%= f.label :password_confirmation, "确认密码", class: "block text-sm font-medium text-gray-700" %>
      <% if @agent.errors[:password_confirmation].present? %>
        <%= f.password_field :password_confirmation, class: "input border-red-500" %>
        <div class="text-red-500 text-sm">
          <%= @agent.errors.full_messages_for(:password_confirmation).first %>
        </div>
      <% else %>
        <%= f.password_field :password_confirmation, required: true, class: "input" %>
      <% end %>
    </div>

    <div class="flex justify-end">
      <%= f.submit "保存设置", class: "btn btn-primary" %>
    </div>
  <% end %>
</div>
