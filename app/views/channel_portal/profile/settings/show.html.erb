<div class="max-w-2xl mt-8 p-6 bg-white rounded-lg shadow-md">
  <h2 class="text-xl mb-6">个人设置</h2>

  <%= form_with(model: @channel, url: channel_portal_profile_setting_path, method: :patch, class: "space-y-6", data: { turbo: false }) do |f| %>
    <% if @channel.errors.any? %>
      <div class="bg-red-50 p-4 rounded-md mb-6">
        <div class="text-red-700">
          <h3 class="text-sm font-medium">请修正以下错误：</h3>
          <ul class="mt-2 list-disc list-inside">
            <% @channel.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>

    <div>
      <%= f.label :name, "渠道名称", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1 text-sm text-gray-900"><%= @channel.name %></div>
    </div>

    <div>
      <%= f.label :agent, "所属代理", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1 text-sm text-gray-900"><%= @channel.agent.name %></div>
    </div>

    <div>
      <%= f.label :promotion_type, "推广类型", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1 text-sm text-gray-900"><%= @channel.promotion_type.name %></div>
    </div>

    <div>
      <%= f.label :nickname, "昵称", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :nickname, class: "input" %>
    </div>

    <% if @channel.advertiser.present? %>
    <div>
      <%= f.label :advertiser, "广告方", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1 text-sm text-gray-900"><%= @channel.advertiser %></div>
    </div>
    <% end %>

    <div class="flex justify-between items-center">
      <div class="text-xs text-gray-500">
        帐号添加时间: <%= @channel.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
      </div>
      <%= f.submit "保存设置", class: "btn btn-primary" %>
    </div>
  <% end %>
</div>
