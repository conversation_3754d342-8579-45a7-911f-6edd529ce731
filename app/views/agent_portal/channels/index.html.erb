<div class="space-y-6 p-4">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-xl">我的渠道</h1>
    <%= link_to "添加渠道", new_agent_portal_channel_path, class: "btn btn-primary btn-sm" %>
  </div>

  <div class="bg-base-100 shadow-xl rounded-lg p-6">
    <!-- 搜索表单 -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex gap-4">
        <%= form_with url: agent_portal_channels_path, method: :get, class: "flex gap-4" do |f| %>
          <div class="form-control">
            <%= f.text_field :search, placeholder: "搜索渠道名称或昵称", class: "input input-bordered w-full", value: params[:search] %>
          </div>

          <div class="form-control">
            <%= f.select :status,
                options_for_select([["全部状态", ""], ["正常", "actived"], ["禁用", "inactived"]], params[:status]),
                {},
                class: "select select-bordered w-full" %>
          </div>

          <div class="form-control">
            <%= f.select :promotion_type_id,
                options_from_collection_for_select(@promotion_types, :id, :name, params[:promotion_type_id]),
                { prompt: "全部推广类型" },
                class: "select select-bordered w-full" %>
          </div>

          <%= f.submit "搜索", class: "btn btn-primary" %>
          <%= link_to "重置", agent_portal_channels_path, class: "btn btn-outline" %>
        <% end %>
      </div>
    </div>

    <% if @channels.any? %>
      <div class="overflow-x-auto">
        <table class="table w-full">
          <thead>
            <tr>
              <th>ID</th>
              <th>渠道名称</th>
              <th>昵称</th>
              <th>推广类型</th>
              <th>状态</th>
              <th>最后登录</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% @channels.each do |channel| %>
              <tr>
                <td><%= channel.id %></td>
                <td>
                  <%= link_to channel.name, agent_portal_channel_path(channel),
                      class: "link link-primary" %>
                </td>
                <td><%= channel.nickname %></td>
                <td><%= channel.promotion_type.name %></td>
                <td>
                  <span class="badge <%= channel.status == 'actived' ? 'badge-success' : 'badge-error' %>">
                    <%= channel.status == 'actived' ? '正常' : '禁用' %>
                  </span>
                </td>
                <td><%= channel.last_login_at&.strftime("%Y-%m-%d %H:%M") || "从未登录" %></td>
                <td><%= channel.created_at.strftime("%Y-%m-%d %H:%M") %></td>
                <td>
                  <div class="flex gap-2">
                    <%= link_to "查看", agent_portal_channel_path(channel), class: "btn btn-sm btn-info" %>
                    <%= link_to "编辑", edit_agent_portal_channel_path(channel), class: "btn btn-sm btn-warning" %>
                    <%= button_to "重置密码", reset_password_agent_portal_channel_path(channel),
                        method: :post,
                        class: "btn btn-sm btn-outline",
                        form: { data: { turbo: true } } %>
                    <%= button_to "删除", agent_portal_channel_path(channel),
                        method: :delete,
                        class: "btn btn-sm btn-error btn-outline",
                        data: { turbo_confirm: "确定要删除渠道 #{channel.name} 吗？" } %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <!-- 分页导航 -->
      <div class="mt-4">
        <%== pagy_nav(@pagy) %>
      </div>
    <% end %>
  </div>
</div>
