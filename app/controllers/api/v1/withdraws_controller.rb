module Api
  module V1
    class WithdrawsController < ApplicationController
      before_action :set_withdraw, only: [ :show, :update ]

      # GET /api/v1/withdraws
      # params:
      #   size: 每页条数
      #   current: 当前页码
      #   user_id:  用户ID
      #   username: 用户名
      #   order_no: 平台订单号
      #   risk: 是否风控
      #   status: 状态，0=待支付 1=完成 2=拒绝 3=没收
      #   start_time ~ end_time 查询时间范围
      def index
        size = (params[:size].presence || 20).to_i.clamp(2, 2000)
        current = (params[:current].presence || 1).to_i.clamp(1, 1000000)
        current = 1 if current < 1

        # Build search query
        query = Withdraw.left_joins(:user, :channel, :agent).select("withdraws.*, users.nickname,users.email_address as email,users.mobile,users.name,channels.name as channel_name,channels.nickname as channel_nickname,agents.name as agent_name,agents.nickname as agent_nickname").order(id: :desc)
        query = query.where("user_id = ?", "#{params[:user_id]}") if params[:user_id].present?
        query = query.where("username = ?", "#{params[:username]}") if params[:username].present?
        query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?
        query = query.where("create_at between ? and ?", "#{params[:start_time]}", "#{params[:end_time]}") if params[:start_time].present? && params[:end_time].present?
        query = query.where(status: params[:status].to_i) if params[:status].present?

        @pagy, @withdraws = pagy(query, limit: size, page: current)

        render json: {
          code: "0000",
          msg: "success",
          data: {
            records: @withdraws,
            total: @pagy.count,
            size: size,
            current: current
          }
        }
      end

      # GET /api/v1/withdraws/1
      def show
        render json: {
          code: "0000",
          msg: "请求成功",
          data: @withdraw
        }
      end

      # PATCH/PUT /api/v1/withdraws/1
      def update
        # 验证状态转换
        if @withdraw.status != 0
          render json: { code: "0001", msg: "该提现申请已经处理过了，无法再次修改", data: nil }
          return
        end

        new_status = withdraw_params[:status].to_i
        unless [1, 2, 3].include?(new_status)
          render json: { code: "0001", msg: "无效的状态值", data: nil }
          return
        end

        # 开启事务处理
        begin
          ActiveRecord::Base.transaction do
            # 更新提现记录
            update_withdraw_record(new_status)

            # 处理钱包余额（仅拒绝时退回余额）
            handle_wallet_balance(new_status) if new_status == 2

            # 记录操作日志
            save_log(new_status)
          end

          render json: { code: "0000", msg: "提现申请处理成功", data: nil }
        rescue => e
          Rails.logger.error "提现申请更新失败: #{e.message}"
          Rails.logger.error e.backtrace.join("\n")
          render json: { code: "0001", msg: "处理失败: #{e.message}", data: nil }
        end
      end

      private

      def set_withdraw
        @withdraw = Withdraw.find(params[:id])
      end

      def withdraw_params
        params.require(:withdraw).permit(
          :status,
          :risk,
          :remark
        )
      end

      # 更新提现记录
      def update_withdraw_record(new_status)
        current_time = Time.current

        update_attrs = {
          status: new_status,
          risk: withdraw_params[:risk],
          remark: withdraw_params[:remark]
        }

        case new_status
        when 1 # 提现成功
          update_attrs[:review_time] = current_time
          update_attrs[:process_time] = current_time
          update_attrs[:finish_time] = current_time
        when 2, 3 # 拒绝或没收
          update_attrs[:review_time] = current_time
          update_attrs[:finish_time] = current_time
        end

        @withdraw.update!(update_attrs)
      end

      # 处理钱包余额
      def handle_wallet_balance(status)
        return unless status == 2 # 只有拒绝时才退回余额

        wallet = Wallet.find_by(user_id: @withdraw.user_id)
        raise "用户钱包不存在" unless wallet

        # 使用 BigDecimal 进行精确计算
        new_balance = wallet.balance + @withdraw.amount
        wallet.update!(balance: new_balance)
      end

      # 记录操作日志
      def save_log(status)
        operate_type, action_desc = case status
        when 1
          [:withdraw_approve, "提现审核通过 - 用户ID: #{@withdraw.user_id}, 订单号: #{@withdraw.order_no}, 金额: #{@withdraw.amount}"]
        when 2
          [:withdraw_reject, "提现拒绝 - 用户ID: #{@withdraw.user_id}, 订单号: #{@withdraw.order_no}, 金额: #{@withdraw.amount}, 原因: #{withdraw_params[:remark]}"]
        when 3
          [:withdraw_confiscate, "提现没收 - 用户ID: #{@withdraw.user_id}, 订单号: #{@withdraw.order_no}, 金额: #{@withdraw.amount}, 原因: #{withdraw_params[:remark]}"]
        else
          [:other, "未知提现操作 - 状态: #{status}"]
        end

        SystemLog.log(
          actor: current_admin,
          loggable: @withdraw,
          operate: operate_type,
          action: action_desc,
          snapshot: {
            withdraw_id: @withdraw.id,
            user_id: @withdraw.user_id,
            order_no: @withdraw.order_no,
            amount: @withdraw.amount.to_s,
            old_status: 0,
            new_status: status,
            remark: withdraw_params[:remark],
            operator: current_admin&.name
          }
        )
      end
    end
  end
end
