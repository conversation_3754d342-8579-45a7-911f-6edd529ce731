#!/usr/bin/env ruby

puts "=== 验证Channel强制修改密码功能实现 ==="

# 检查文件是否存在
files_to_check = [
  "app/models/channel.rb",
  "app/controllers/channel_portal/application_controller.rb",
  "app/controllers/channel_portal/force_password_changes_controller.rb",
  "app/controllers/channel_portal/sessions_controller.rb",
  "app/views/channel_portal/force_password_changes/show.html.erb",
  "config/routes.rb"
]

puts "\n1. 检查文件是否存在:"
files_to_check.each do |file|
  if File.exist?(file)
    puts "✅ #{file}"
  else
    puts "❌ #{file} - 文件不存在"
  end
end

# 检查Channel模型中的方法
puts "\n2. 检查Channel模型中的using_default_password?方法:"
channel_content = File.read("app/models/channel.rb")
if channel_content.include?("def using_default_password?")
  puts "✅ Channel模型包含using_default_password?方法"
else
  puts "❌ Channel模型缺少using_default_password?方法"
end

if channel_content.include?("DEFAULT_PASSWORD")
  puts "✅ Channel模型包含DEFAULT_PASSWORD常量"
else
  puts "❌ Channel模型缺少DEFAULT_PASSWORD常量"
end

# 检查ApplicationController中的before_action
puts "\n3. 检查Channel Portal ApplicationController中的before_action:"
app_controller_content = File.read("app/controllers/channel_portal/application_controller.rb")
if app_controller_content.include?("before_action :check_default_password")
  puts "✅ ApplicationController包含check_default_password before_action"
else
  puts "❌ ApplicationController缺少check_default_password before_action"
end

if app_controller_content.include?("def check_default_password")
  puts "✅ ApplicationController包含check_default_password方法"
else
  puts "❌ ApplicationController缺少check_default_password方法"
end

# 检查ForcePasswordChangesController
puts "\n4. 检查ForcePasswordChangesController:"
force_controller_content = File.read("app/controllers/channel_portal/force_password_changes_controller.rb")
if force_controller_content.include?("skip_before_action :check_default_password")
  puts "✅ ForcePasswordChangesController跳过密码检查"
else
  puts "❌ ForcePasswordChangesController没有跳过密码检查"
end

if force_controller_content.include?("def show") && force_controller_content.include?("def update")
  puts "✅ ForcePasswordChangesController包含show和update方法"
else
  puts "❌ ForcePasswordChangesController缺少必要的方法"
end

# 检查SessionsController中的登录逻辑
puts "\n5. 检查SessionsController中的登录逻辑:"
sessions_content = File.read("app/controllers/channel_portal/sessions_controller.rb")
if sessions_content.include?("using_default_password?")
  puts "✅ SessionsController包含默认密码检查"
else
  puts "❌ SessionsController缺少默认密码检查"
end

if sessions_content.include?("skip_before_action :check_default_password")
  puts "✅ SessionsController跳过密码检查"
else
  puts "❌ SessionsController没有跳过密码检查"
end

# 检查路由配置
puts "\n6. 检查路由配置:"
routes_content = File.read("config/routes.rb")
if routes_content.include?("resource :force_password_change") && routes_content.include?("channel_portal")
  puts "✅ 路由包含channel_portal的force_password_change资源"
else
  puts "❌ 路由缺少channel_portal的force_password_change资源"
end

# 检查视图文件
puts "\n7. 检查视图文件:"
if File.exist?("app/views/channel_portal/force_password_changes/show.html.erb")
  view_content = File.read("app/views/channel_portal/force_password_changes/show.html.erb")
  if view_content.include?("安全提醒")
    puts "✅ 视图文件包含安全提醒"
  else
    puts "❌ 视图文件缺少安全提醒"
  end

  if view_content.include?("channel_portal_force_password_change_path")
    puts "✅ 视图文件包含正确的表单路径"
  else
    puts "❌ 视图文件缺少正确的表单路径"
  end
else
  puts "❌ 视图文件不存在"
end

# 检查测试fixtures
puts "\n8. 检查测试fixtures:"
if File.exist?("test/fixtures/channels.yml")
  fixtures_content = File.read("test/fixtures/channels.yml")
  if fixtures_content.include?("default_password_channel")
    puts "✅ fixtures包含默认密码的channel"
  else
    puts "❌ fixtures缺少默认密码的channel"
  end
else
  puts "❌ channels.yml fixtures文件不存在"
end

puts "\n=== 验证完成 ==="
puts "如果所有项目都显示✅，说明Channel强制密码修改功能实现是完整的"
