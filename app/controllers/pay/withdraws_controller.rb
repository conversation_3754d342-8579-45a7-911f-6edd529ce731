class Pay::WithdrawsController < ApplicationController
  before_action :set_withdraw, only: [ :edit, :update ]

  # 提现订单
  # GET /pay/withdraws
  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # add search params by name
    search_params = params[:search]
    query = Withdraw.left_joins(:user, :channel, :agent).select("withdraws.*, users.nickname,users.mobile,users.name,
      channels.name as channel_name,channels.nickname as channel_nickname,agents.name as agent_name,agents.nickname as agent_nickname").order(id: :desc)
    query = query.where("user_id = ?", "#{params[:user_id]}") if params[:user_id].present?
    query = query.where("username = ?", "#{params[:username]}") if params[:username].present?
    query = query.where("order_no = ?", "#{params[:order_no]}") if params[:order_no].present?
    query = query.where("create_at between ? and ?", "#{params[:start_time]}", "#{params[:end_time]}") if params[:start_time].present? && params[:end_time].present?
    query = query.where(status: params[:status].to_i) if params[:status].present?

    @pagy, @withdraws = pagy(query, limit: size, page: current)
  end

  def edit
  end

  # PATCH/PUT /pay/withdraws/1
  def update
    # 验证状态转换 - 使用 enum API
    unless @withdraw.pending?
      redirect_to pay_withdraws_path, alert: "该提现申请已经处理过了，无法再次修改"
      return
    end

    new_status_param = withdraw_params[:status]
    # 验证新状态是否有效
    unless %w[success rejected confiscated].include?(new_status_param)
      redirect_to pay_withdraws_path, alert: "无效的状态值"
      return
    end

    # 开启事务处理
    begin
      ActiveRecord::Base.transaction do
        # 更新提现记录
        update_withdraw_record(new_status_param)

        # 处理钱包余额（仅拒绝时退回余额）
        handle_wallet_balance(new_status_param) if new_status_param == 'rejected'

        # 记录操作日志
        save_log(new_status_param)
      end

      redirect_to pay_withdraws_path, notice: "提现申请处理成功"
    rescue => e
      Rails.logger.error "提现申请更新失败: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      redirect_to pay_withdraws_path, alert: "处理失败: #{e.message}"
    end
  end

    private

    def set_withdraw
      @withdraw = Withdraw.find(params[:id])
    end

    def withdraw_params
      params.require(:withdraw).permit(
          :status,
          :risk,
          :remark
      )
    end

    # 更新提现记录
    def update_withdraw_record(new_status_param)
      current_time = Time.current

      update_attrs = {
        status: new_status_param,
        risk: withdraw_params[:risk],
        remark: withdraw_params[:remark]
      }

      case new_status_param
      when 'success' # 提现成功
        update_attrs[:review_time] = current_time
        update_attrs[:process_time] = current_time
        update_attrs[:finish_time] = current_time
      when 'rejected', 'confiscated' # 拒绝或没收
        update_attrs[:review_time] = current_time
        update_attrs[:finish_time] = current_time
      end

      @withdraw.update!(update_attrs)
    end

    # 处理钱包余额
    def handle_wallet_balance(status_param)
      return unless status_param == 'rejected' # 只有拒绝时才退回余额

      wallet = Wallet.find_by(user_id: @withdraw.user_id)
      raise "用户钱包不存在" unless wallet

      # 使用 BigDecimal 进行精确计算
      new_balance = wallet.balance + @withdraw.amount
      wallet.update!(balance: new_balance)
    end

    # 记录操作日志
    def save_log(status_param)
      operate_type, action_desc = case status_param
      when 'success'
        [:withdraw_approve, "提现审核通过 - 用户ID: #{@withdraw.user_id}, 订单号: #{@withdraw.order_no}, 金额: #{@withdraw.amount}"]
      when 'rejected'
        [:withdraw_reject, "提现拒绝 - 用户ID: #{@withdraw.user_id}, 订单号: #{@withdraw.order_no}, 金额: #{@withdraw.amount}, 原因: #{withdraw_params[:remark]}"]
      when 'confiscated'
        [:withdraw_confiscate, "提现没收 - 用户ID: #{@withdraw.user_id}, 订单号: #{@withdraw.order_no}, 金额: #{@withdraw.amount}, 原因: #{withdraw_params[:remark]}"]
      else
        [:other, "未知提现操作 - 状态: #{status_param}"]
      end

      SystemLog.log(
        actor: current_admin,
        loggable: @withdraw,
        operate: operate_type,
        action: action_desc,
        snapshot: {
          withdraw_id: @withdraw.id,
          user_id: @withdraw.user_id,
          order_no: @withdraw.order_no,
          amount: @withdraw.amount.to_s,
          old_status: 'pending',
          new_status: status_param,
          remark: withdraw_params[:remark],
          operator: current_admin&.name
        }
      )
    end
end
