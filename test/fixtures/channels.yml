<% password_digest = BCrypt::Password.create("password") %>
<% default_password_digest = BCrypt::Password.create(Channel::DEFAULT_PASSWORD) %>

default_password_channel:
  name: "Default<PERSON>hannel"
  nickname: "Default"
  email_address: "<EMAIL>"
  mobile: "13800138005"
  password_digest: <%= default_password_digest %>
  status: 0
  timezone: "UTC"
  login_count: 0
  advertiser: "Default Advertiser"
  pixel_id: "default123"
  pixel_token: "default_token"
  agent: active_agent
  promotion_type: facebook

active_channel:
  name: "ActiveChannel"
  nickname: "Active"
  email_address: "<EMAIL>"
  mobile: "13800138004"
  password_digest: <%= password_digest %>
  status: 0
  timezone: "UTC"
  login_count: 3
  last_login_at: <%= Time.current %>
  last_login_ip: "127.0.0.1"
  last_login_user_agent: "Mozilla/5.0"
  advertiser: "Test Advertiser"
  pixel_id: "123456789"
  pixel_token: "test_token_123"
  agent: active_agent
  promotion_type: facebook

inactive_channel:
  name: "InactiveChannel"
  nickname: "Inactive"
  email_address: "<EMAIL>"
  mobile: "13800138005"
  password_digest: <%= password_digest %>
  status: 1
  timezone: "Asia/Shanghai"
  login_count: 0
  advertiser: "Another Advertiser"
  pixel_id: "987654321"
  pixel_token: "test_token_456"
  agent: active_agent
  promotion_type: google

google_channel:
  name: "GoogleChannel"
  nickname: "Google"
  email_address: "<EMAIL>"
  mobile: "13800138006"
  password_digest: <%= password_digest %>
  status: 0
  timezone: "UTC"
  login_count: 1
  advertiser: "Google Advertiser"
  pixel_id: "111222333"
  pixel_token: "test_token_789"
  agent: inactive_agent
  promotion_type: google