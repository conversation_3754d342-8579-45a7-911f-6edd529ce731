require "test_helper"

class AgentPortal::Profile::PasswordsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @agent = agents(:active_agent)
  end

  test "should show password form when agent is logged in" do
    sign_in_as @agent
    get agent_portal_profile_password_path
    assert_response :success
    assert_select "h2", "安全设置"
    assert_select "input[type=password][name='agent[old_password]']"
    assert_select "input[type=password][name='agent[password]']"
    assert_select "input[type=password][name='agent[password_confirmation]']"
  end

  test "should update password with correct old password" do
    sign_in_as @agent
    patch agent_portal_profile_password_path, params: {
      agent: {
        old_password: "password",
        password: "newpassword123",
        password_confirmation: "newpassword123"
      }
    }
    assert_redirected_to agent_portal_profile_password_path
    assert_equal "密码已更新", flash[:notice]
  end

  test "should not update password with incorrect old password" do
    sign_in_as @agent
    patch agent_portal_profile_password_path, params: {
      agent: {
        old_password: "wrongpassword",
        password: "newpassword123",
        password_confirmation: "newpassword123"
      }
    }
    assert_response :unprocessable_entity
    # 检查响应中是否包含错误信息
    assert_select "div.text-red-500", text: /错误/
  end

  test "should not update password when new passwords don't match" do
    sign_in_as @agent
    patch agent_portal_profile_password_path, params: {
      agent: {
        old_password: "password",
        password: "newpassword123",
        password_confirmation: "differentpassword"
      }
    }
    assert_response :unprocessable_entity
  end

  test "should require authentication for show" do
    get agent_portal_profile_password_path
    assert_response :redirect
  end

  test "should require authentication for update" do
    patch agent_portal_profile_password_path, params: {
      agent: {
        old_password: "password",
        password: "newpassword123",
        password_confirmation: "newpassword123"
      }
    }
    assert_response :redirect
  end
end
