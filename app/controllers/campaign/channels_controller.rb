class Campaign::ChannelsController < ApplicationController
  # 运营管理模块: 渠道管理 -> 渠道管理
  before_action :set_channel, only: [ :edit, :update, :destroy, :show, :reset_password ]

  def index
    size = (params[:size].presence || 10).to_i.clamp(2, 2000)
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)

    # Need filter deleted channels
    channel_scope = Channel.where.not(status: :deleted).includes(:agent)

    # add search params by name
    search_params = params[:search]
    if search_params.present?
      channel_scope = channel_scope.where("name LIKE ? OR nickname LIKE ?", "%#{search_params}%", "%#{search_params}%")
    end

    if params[:status].present?
      channel_scope = channel_scope.where(status: params[:status])
    end

    # order
    if params[:order].present?
      channel_scope = channel_scope.order(params[:order])
    else
      channel_scope = channel_scope.order(id: :desc)
    end

    @pagy, @channels = pagy(channel_scope, limit: size, page: current)
  end

  def new
    @channel = Channel.new
  end

  def edit
  end

  def create
    @channel = Channel.new(channel_params)
    @channel.nickname ||= @channel.name
    @channel.password ||= Channel::DEFAULT_PASSWORD
    # @channel.created_by = Current.admin&.name
    @channel.timezone ||= "UTC"

    if @channel.save
      SystemLog.log(
        actor: Current.admin,
        loggable: @channel,
        operate: :create_channel,
        action: "创建渠道",
        snapshot: @channel.attributes
      )
      redirect_to campaign_channel_path(@channel), notice: "渠道 #{@channel.name} 添加成功"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @channel.update(channel_params)
      SystemLog.log(
        actor: Current.admin,
        loggable: @channel,
        operate: :update_channel,
        action: "更新渠道",
        snapshot: @channel.attributes
      )
      redirect_to campaign_channels_path, notice: "渠道 #{@channel.name} 修改成功"
    else
      set_promotion_types
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @channel.update(status: :deleted)
      SystemLog.log(
        actor: Current.admin,
        loggable: @channel,
        operate: :destroy_channel,
        action: "删除渠道",
        snapshot: @channel.attributes
      )
      redirect_to campaign_channels_path, notice: "删除成功"
    else
      redirect_to campaign_channels_path, alert: "删除失败"
    end
  end

  def show
  end

  def reset_password
    password = params[:password].presence || SecureRandom.hex(8)
    if password.length < 8
      flash.now[:alert] = "密码长度不能少于8位"
      return render turbo_stream: turbo_stream.update(
        "flash_messages",
        partial: "shared/flash_messages"
      )
    end

    begin
      @channel.update!(password: password)
    rescue ActiveRecord::RecordInvalid => e
      flash.now[:alert] = "密码重置失败: #{e.message}"
      render turbo_stream: turbo_stream.replace(
        "flash_messages",
        partial: "shared/flash_messages"
      )
    else
      SystemLog.log(
        actor: Current.admin,
        loggable: @channel,
        operate: :reset_channel_password,
        action: "重置渠道密码",
        snapshot: {
          name: @channel.name,
          nickname: @channel.nickname,
          remark: @channel.remark
        }
      )
      render turbo_stream: turbo_stream.replace(
        "modal",
        partial: "campaign/channels/password_modal",
        locals: { password: password }
      )
    end
  end

  private

  def set_channel
    @channel = Channel.where.not(status: :deleted).find(params[:id])
  end

  def channel_params
    params.require(:channel).permit(
      :name,
      :nickname,
      :email_address,
      :mobile,
      :remark,
      :status,
      :timezone,
      :password,
      :agent_id,
      :promotion_type_id
    )
  end
end
