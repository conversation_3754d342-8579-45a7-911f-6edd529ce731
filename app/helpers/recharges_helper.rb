module Re<PERSON><PERSON><PERSON><PERSON><PERSON>
  def order_status_badge(status)
    status_map = {
      pending: { text: "支付中", bg: "bg-yellow-100", text_color: "text-yellow-800" },
      success: { text: "成功", bg: "bg-green-100", text_color: "text-green-800" },
      failed: { text: "失败", bg: "bg-red-100", text_color: "text-red-800" },
      callback_failed: { text: "回调失败", bg: "bg-orange-100", text_color: "text-orange-800" },
      handle_failed: { text: "处理失败", bg: "bg-purple-100", text_color: "text-purple-800" }
    }

    style = status_map[status.to_sym] || { text: "未知", bg: "bg-gray-100", text_color: "text-gray-800" }

    tag.span(style[:text], class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{style[:bg]} #{style[:text_color]}")
  end
end
