#!/usr/bin/env ruby
# 演示pending消息恢复功能

require_relative '../config/environment'

def demo_pending_recovery
  puts "=== Redis Stream Pending Message Recovery Demo ==="
  puts

  stream_key = RedisStreamProcessor::STREAM_KEY
  group_name = RedisStreamProcessor::GROUP_NAME
  consumer_name = RedisStreamProcessor::CONSUMER_NAME

  Rails.cache.redis.with do |redis_client|
    # 1. 清理环境
    puts "1. 清理测试环境..."
    begin
      redis_client.del(stream_key)
      redis_client.xgroup(:create, stream_key, group_name, "0", mkstream: true)
      puts "   ✅ 环境清理完成"
    rescue => e
      puts "   ⚠️  环境清理出错: #{e.message}"
    end

    # 2. 添加测试消息
    puts "\n2. 添加测试消息..."
    test_messages = []
    3.times do |i|
      entry_id = redis_client.xadd(stream_key, {
        user_id: (i + 1).to_s,
        bet: "100",
        real_win: "80",
        win: "80",
        game_number: "demo_game_#{i + 1}",
        round_id: "demo_round_#{i + 1}",
        before_money: "1000",
        after_money: "1080",
        bet_time: Time.current.to_i.to_s
      })
      test_messages << entry_id
      puts "   ✅ 消息 #{i + 1}: #{entry_id}"
    end

    # 3. 模拟消费者读取但不确认（模拟崩溃）
    puts "\n3. 模拟消费者读取消息但不确认（模拟崩溃场景）..."
    messages = redis_client.xreadgroup(
      group_name,
      consumer_name,
      stream_key,
      ">",
      count: 3
    )
    puts "   ✅ 读取了 #{messages.first[1].size} 条消息但未确认"

    # 4. 检查pending状态
    puts "\n4. 检查pending消息状态..."
    pending_info = redis_client.xpending(stream_key, group_name)
    puts "   📊 Pending消息数量: #{pending_info['size']}"

    if pending_info['size'] > 0
      detailed_pending = redis_client.xpending(stream_key, group_name, "-", "+", 10)
      detailed_pending.each_with_index do |msg, index|
        puts "   📝 消息 #{index + 1}: ID=#{msg['entry_id']}, 消费者=#{msg['consumer']}, 空闲时间=#{msg['elapsed']}ms"
      end
    end

    # 5. 等待消息超时
    puts "\n5. 等待消息超时（等待2秒）..."
    sleep 2

    # 6. 执行pending消息恢复
    puts "\n6. 执行pending消息恢复..."

    # Mock DynamodbLogger to avoid actual DynamoDB calls in demo
    original_log_bet = DynamodbLogger.method(:log_bet)
    DynamodbLogger.define_singleton_method(:log_bet) do |**args|
      puts "   💾 模拟写入DynamoDB: user_id=#{args[:user_id]}, bet=#{args[:bet]}, game=#{args[:game_number]}"
      true
    end

    begin
      processor = RedisStreamProcessor.new
      # 临时修改超时时间为1秒用于演示
      original_timeout = RedisStreamProcessor::PENDING_TIMEOUT
      RedisStreamProcessor.const_set(:PENDING_TIMEOUT, 1)

      processor.send(:process_pending_messages)
      puts "   ✅ Pending消息恢复完成"

      # 恢复原始超时时间
      RedisStreamProcessor.const_set(:PENDING_TIMEOUT, original_timeout)
    rescue => e
      puts "   ❌ 恢复失败: #{e.message}"
    ensure
      # 恢复原始方法
      DynamodbLogger.define_singleton_method(:log_bet, original_log_bet)
    end

    # 7. 验证恢复结果
    puts "\n7. 验证恢复结果..."
    final_pending_info = redis_client.xpending(stream_key, group_name)
    puts "   📊 恢复后Pending消息数量: #{final_pending_info['size']}"

    if final_pending_info['size'] == 0
      puts "   🎉 所有pending消息已成功恢复！"
    else
      puts "   ⚠️  仍有 #{final_pending_info['size']} 条消息未恢复"
    end

    # 8. 清理
    puts "\n8. 清理测试数据..."
    redis_client.del(stream_key)
    puts "   ✅ 清理完成"
  end

  puts "\n=== Demo 完成 ==="
end

# 运行演示
if __FILE__ == $0
  demo_pending_recovery
end
