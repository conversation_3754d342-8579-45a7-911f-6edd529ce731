module AgentPortal
  module Profile
    class PasswordsController < AgentPortal::ApplicationController
      def show
        @agent = Current.agent
      end

      def update
        @agent = Current.agent

        if @agent.authenticate(agent_params[:old_password])
          if @agent.update(agent_params.except(:old_password))
            redirect_to agent_portal_profile_password_path, notice: "密码已更新"
          else
            render :show, status: :unprocessable_entity
          end
        else
          @agent.errors.add(:old_password, "错误")
          render :show, status: :unprocessable_entity
        end
      end

      private

      def agent_params
        params.require(:agent).permit(:old_password, :password, :password_confirmation)
      end
    end
  end
end
