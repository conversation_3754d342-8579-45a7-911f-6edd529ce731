class Setting < ApplicationRecord
  # 值类型定义
  VALUE_TYPES = %w[string integer float boolean json array].freeze

  # 验证
  validates :key, presence: true, uniqueness: { scope: :section }
  validates :label, presence: true
  validates :value_type, presence: true, inclusion: { in: VALUE_TYPES }
  validates :section, presence: true

  # 缓存键
  CACHE_KEY_PREFIX = "setting:".freeze

  # 获取配置值
  # @param key [String] 配置键
  # @param section [String] 配置分组
  # @param default [Object] 默认值
  # @return [Object] 配置值
  def self.get(key, section: "system", default: nil)
    cache_key = "#{CACHE_KEY_PREFIX}#{section}:#{key}"

    Rails.cache.fetch(cache_key) do
      setting = find_by(key: key.to_s, section: section.to_s)
      return default unless setting

      setting.typed_value
    end
  end

  # 设置配置值
  # @param key [String] 配置键
  # @param value [Object] 配置值
  # @param section [String] 配置分组
  # @param options [Hash] 其他选项
  # @return [Setting] 配置对象
  def self.set(key, value, section: "system", **options)
    setting = find_or_initialize_by(key: key, section: section)

    # 设置默认值
    options[:label] ||= key.titleize
    options[:value_type] ||= infer_value_type(value)

    # 更新属性
    setting.assign_attributes(
      value: value.to_s,
      value_type: options[:value_type],
      label: options[:label],
      description: options[:description],
      options: options[:options],
      status: options[:status] || 0
    )

    if setting.save
      # 清除缓存
      Rails.cache.delete("#{CACHE_KEY_PREFIX}#{section}:#{key}")
      setting
    else
      false
    end
  end

  # 获取分组下的所有配置
  # @param section [String] 配置分组
  # @return [Hash] 配置哈希
  def self.get_section(section, json: false)
    cache_key = "#{CACHE_KEY_PREFIX}section:#{section}"

    json_string = Rails.cache.fetch(cache_key, raw: true) do
      settings = where(section: section).order(sort: :asc)
      settings.each_with_object({}) do |setting, hash|
        hash[setting.key] = setting.typed_value
      end.to_json
    end

    return json_string unless json

    # if parsed failed, clear cache and return empty hash
    begin
      JSON.parse(json_string)
    rescue JSON::ParserError
      Rails.cache.delete(cache_key)
      # 重新获取数据并缓存
      settings = where(section: section).order(sort: :asc)
      result = settings.each_with_object({}) do |setting, hash|
        hash[setting.key] = setting.typed_value
      end
      Rails.cache.write(cache_key, result.to_json, raw: true)
      result
    end
  end

  def self.notify_section_refreshed(section)
    cache_key = "#{CACHE_KEY_PREFIX}section:#{section}"
    Rails.cache.delete(cache_key)
    json_string = get_section(section)
    Rails.cache.redis.with do |redis_client|
      redis_client.publish(cache_key, json_string)
      # Related test cmd in redis-cli: SUBSCRIBE settings:#{section}
    end
  end

  def self.notify_all_refreshed
    cache_key = "#{CACHE_KEY_PREFIX}all"
    Rails.cache.delete(cache_key)
    # Get all settings
    settings = all
    json_string = settings.each_with_object({}) do |setting, hash|
      hash[setting.key] = setting.typed_value
    end.to_json

    Rails.cache.write(cache_key, json_string, raw: true)

    Rails.cache.redis.with do |redis_client|
      redis_client.publish(cache_key, json_string)
      # Related test cmd in redis-cli: SUBSCRIBE settings:all
    end
  end

  def self.get_keys(section)
    cache_key = "#{CACHE_KEY_PREFIX}section_keys:#{section}"

    Rails.cache.fetch(cache_key) do
      where(section: section.to_s).order(sort: :asc).pluck(:key)
    end
  end

  # 获取所有配置分组
  # @return [Array<String>] 配置分组列表
  def self.sections
    cache_key = "#{CACHE_KEY_PREFIX}sections"

    Rails.cache.fetch(cache_key) do
      select(:section).distinct.pluck(:section).sort
    end
  end

  # 获取类型转换后的值
  def typed_value
    case value_type
    when "integer"
      value.to_i
    when "float"
      value.to_f
    when "boolean"
      value == "true" || value == "1" || value == "t"
    when "json"
      JSON.parse(value) rescue value
    when "array"
      value.split(",").map(&:strip)
    else
      value
    end
  end

  def parsed_typed_value(input_value)
    case value_type
    when "integer"
      input_value.to_i
    when "float"
      input_value.to_f
    when "boolean"
      input_value == "true" || input_value == "1" || input_value == "t"
    when "json"
      JSON.parse(input_value) rescue input_value
    when "array"
      input_value.split(",").map(&:strip)
    else
      input_value
    end
  end

  def section_and_key
    "#{section}.#{key}"
  end

  enum :status, { enabled: 0, disabled: 1 }, default: :enabled
  def status_text
    I18n.t("common.status.#{status}")
  end

  private

  # 推断值的类型
  def self.infer_value_type(value)
    case value
    when Integer
      "integer"
    when Float
      "float"
    when TrueClass, FalseClass
      "boolean"
    when Array
      "array"
    when Hash
      "json"
    else
      "string"
    end
  end
end
