require "test_helper"

class Campaign::SettingsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @admin = admins(:one)
    # 创建一些测试设置
    @setting1 = Setting.create!(
      key: "test_key1",
      label: "Test Setting 1",
      value: "value1",
      value_type: "string",
      section: "register_login"
    )
    @setting2 = Setting.create!(
      key: "test_key2",
      label: "Test Setting 2",
      value: "value2",
      value_type: "string",
      section: "message"
    )
  end

  test "should show settings for valid section" do
    sign_in_as @admin
    get campaign_setting_path(section: "register_login")
    assert_response :success
    assert_select "form"
  end

  test "should redirect for invalid section" do
    sign_in_as @admin
    get campaign_setting_path(section: "invalid_section")
    assert_response :redirect
    assert_match "无效的配置项", flash[:alert]
  end

  test "should update settings for valid section" do
    sign_in_as @admin
    patch campaign_setting_path(section: "register_login"), params: {
      settings: { test_key1: "updated_value" }
    }
    assert_response :redirect
    assert_match "设置已更新", flash[:notice]

    # 验证设置已更新
    @setting1.reload
    assert_equal "updated_value", @setting1.value
  end

  test "should redirect update for invalid section" do
    sign_in_as @admin
    patch campaign_setting_path(section: "invalid_section"), params: {
      settings: { test_key1: "updated_value" }
    }
    assert_response :redirect
    assert_match "无效的配置项", flash[:alert]
  end

  test "should require authentication" do
    get campaign_setting_path(section: "register_login")
    assert_response :redirect
  end
end
