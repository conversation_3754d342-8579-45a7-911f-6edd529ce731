require "test_helper"

class RedisStreamProcessorTest < ActiveSupport::TestCase
  setup do
    # 为每个测试使用唯一的 stream 名称避免并行测试冲突
    @test_id = SecureRandom.hex(8)
    @original_stream_key = RedisStreamProcessor::STREAM_KEY
    @original_group_name = RedisStreamProcessor::GROUP_NAME
    @original_consumer_name = RedisStreamProcessor::CONSUMER_NAME

    # 临时修改常量用于测试
    RedisStreamProcessor.const_set(:STREAM_KEY, "#{@original_stream_key}_test_#{@test_id}")
    RedisStreamProcessor.const_set(:GROUP_NAME, "#{@original_group_name}_test_#{@test_id}")
    RedisStreamProcessor.const_set(:CONSUMER_NAME, "#{@original_consumer_name}_test_#{@test_id}")

    @processor = RedisStreamProcessor.new

    # 确保每个测试开始时都有干净的 Redis Stream 环境
    Rails.cache.redis.with do |redis_client|
      begin
        # 删除可能存在的 stream
        redis_client.del(RedisStreamProcessor::STREAM_KEY)
        # 创建新的 stream 和 consumer group
        redis_client.xgroup(:create, RedisStreamProcessor::STREAM_KEY, RedisStreamProcessor::GROUP_NAME, "0", mkstream: true)
      rescue => e
        puts "Setup failed: #{e.message}"
      end
    end
  end

  teardown do
    # 停止处理器
    @processor.stop if @processor

    # 清理测试数据
    Rails.cache.redis.with do |redis_client|
      begin
        # 删除 stream（这会自动删除相关的 consumer group）
        redis_client.del(RedisStreamProcessor::STREAM_KEY)
      rescue => e
        puts "Teardown failed: #{e.message}"
      end
    end

    # 恢复原始常量
    RedisStreamProcessor.const_set(:STREAM_KEY, @original_stream_key)
    RedisStreamProcessor.const_set(:GROUP_NAME, @original_group_name)
    RedisStreamProcessor.const_set(:CONSUMER_NAME, @original_consumer_name)
  end

  test "should check pending messages when needed" do
    # 初始状态不需要检查
    assert_not @processor.send(:should_check_pending_messages?)

    # 设置上次检查时间为很久以前
    @processor.instance_variable_set(:@last_pending_check, Time.current - 400)
    assert @processor.send(:should_check_pending_messages?)
  end

  test "should process pending messages" do
    # 添加测试消息
    entry_id = nil
    Rails.cache.redis.with do |redis_client|
      entry_id = redis_client.xadd(RedisStreamProcessor::STREAM_KEY, {
        user_id: "1",
        bet: "100",
        real_win: "80",
        win: "80",
        game_number: "test_game",
        round_id: "test_round",
        before_money: "1000",
        after_money: "1080",
        bet_time: Time.current.to_i.to_s
      })
    end
    assert entry_id, "Failed to add test message to stream"

    # 读取消息但不确认（模拟消费者崩溃）
    Rails.cache.redis.with do |redis_client|
      messages = redis_client.xreadgroup(
        RedisStreamProcessor::GROUP_NAME,
        RedisStreamProcessor::CONSUMER_NAME,
        RedisStreamProcessor::STREAM_KEY,
        ">",
        count: 1
      )
      assert_not_empty messages, "Failed to read message from stream"
    end

    # 等待消息变为pending状态并超时
    sleep 2

    # Mock DynamodbLogger to avoid actual DynamoDB calls
    DynamodbLogger.stub :log_bet, true do
      # 处理pending消息
      assert_nothing_raised do
        @processor.send(:process_pending_messages)
      end
    end

    # 验证消息已被确认（pending列表应该为空）
    Rails.cache.redis.with do |redis_client|
      pending_messages = redis_client.xpending(
        RedisStreamProcessor::STREAM_KEY,
        RedisStreamProcessor::GROUP_NAME,
        "-", "+", 100
      )
      assert_empty pending_messages, "Pending messages should be empty after processing"
    end
  end

  test "should handle missing message details gracefully" do
    # 创建一个不存在的entry_id
    fake_entry_id = "9999999999999-0"

    # Mock pending messages response
    Rails.cache.redis.with do |redis_client|
      # 这个测试验证当消息详情不存在时的处理
      assert_nothing_raised do
        @processor.send(:process_pending_message, fake_entry_id, {
          "user_id" => "1",
          "bet" => "100",
          "real_win" => "80",
          "win" => "80",
          "game_number" => "test",
          "round_id" => "test",
          "before_money" => "1000",
          "after_money" => "1080",
          "bet_time" => Time.current.to_i.to_s
        })
      end
    end
  end

  test "should handle redis errors gracefully" do
    # 使用不存在的stream测试错误处理
    original_stream_key = RedisStreamProcessor::STREAM_KEY
    RedisStreamProcessor.const_set(:STREAM_KEY, "nonexistent_stream")

    assert_nothing_raised do
      @processor.send(:process_pending_messages)
    end

    # 恢复原始stream key
    RedisStreamProcessor.const_set(:STREAM_KEY, original_stream_key)
  end

  test "should stop gracefully" do
    assert @processor.instance_variable_get(:@running)

    @processor.stop

    assert_not @processor.instance_variable_get(:@running)
  end
end
