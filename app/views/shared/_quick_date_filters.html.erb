<!-- 快捷日期过滤链接 -->
<div class="mt-4 pt-4 border-t border-gray-200">
  <div class="flex flex-col gap-3">
    <!-- 单日快捷选择 -->
    <div class="flex flex-col sm:flex-row gap-2">
      <span class="text-sm font-medium text-gray-700 self-center">快捷日期：</span>
      <div class="flex flex-wrap gap-2">
        <%
          today = Date.current
          # 安全地获取查询参数
          safe_params = {
            'search' => params[:search],
            'order_no' => params[:order_no],
            'status' => params[:status],
            'first_recharge' => params[:first_recharge]
          }.compact

          (0..6).each do |days_ago|
            date = today - days_ago.days
            date_str = date.strftime('%Y-%m-%d')
            display_name = case days_ago
              when 0 then "今天"
              when 1 then "昨天"
              when 2 then "前天"
              else "#{date.strftime('%m-%d')}(#{%w[日 一 二 三 四 五 六][date.wday]})"
            end

            # 检查当前是否选择了这个日期
            is_active = params[:start_time] == date_str && params[:end_time] == date_str
            css_class = is_active ? "btn btn-primary btn-sm" : "btn btn-outline btn-sm"

            # 构建查询参数，保留现有的搜索条件
            query_params = safe_params.merge(
              'start_time' => date_str,
              'end_time' => date_str
            )
        %>
          <%= link_to display_name,
              url_for(query_params),
              class: css_class %>
        <% end %>
      </div>
    </div>

    <!-- 时间段快捷选择 -->
    <div class="flex flex-col sm:flex-row gap-2">
      <span class="text-sm font-medium text-gray-700 self-center">时间段：</span>
      <div class="flex flex-wrap gap-2">
        <%
          # 本周（周一到今天）
          week_start = today.beginning_of_week
          week_start_str = week_start.strftime('%Y-%m-%d')
          today_str = today.strftime('%Y-%m-%d')
          is_this_week = params[:start_time] == week_start_str && params[:end_time] == today_str
          week_css = is_this_week ? "btn btn-primary btn-sm" : "btn btn-outline btn-sm"

          # 本月（月初到今天）
          month_start = today.beginning_of_month
          month_start_str = month_start.strftime('%Y-%m-%d')
          is_this_month = params[:start_time] == month_start_str && params[:end_time] == today_str
          month_css = is_this_month ? "btn btn-primary btn-sm" : "btn btn-outline btn-sm"

          # 最近7天
          seven_days_ago = (today - 6.days).strftime('%Y-%m-%d')
          is_last_7_days = params[:start_time] == seven_days_ago && params[:end_time] == today_str
          seven_days_css = is_last_7_days ? "btn btn-primary btn-sm" : "btn btn-outline btn-sm"
        %>

        <%= link_to "本周",
            url_for(safe_params.merge(
              'start_time' => week_start_str,
              'end_time' => today_str
            )),
            class: week_css %>

        <%= link_to "最近7天",
            url_for(safe_params.merge(
              'start_time' => seven_days_ago,
              'end_time' => today_str
            )),
            class: seven_days_css %>

        <%= link_to "本月",
            url_for(safe_params.merge(
              'start_time' => month_start_str,
              'end_time' => today_str
            )),
            class: month_css %>
      </div>
    </div>
  </div>
</div>
