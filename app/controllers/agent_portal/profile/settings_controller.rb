module AgentPortal
  module Profile
    class SettingsController < AgentPortal::ApplicationController
      def show
        @agent = Current.agent
      end

      def update
        @agent = Current.agent
        if @agent.update(agent_params)
          redirect_to agent_portal_profile_setting_path, notice: "个人设置已更新"
        else
          render :show, status: :unprocessable_entity
        end
      end

      private

      def agent_params
        params.require(:agent).permit(:nickname)
      end
    end
  end
end
