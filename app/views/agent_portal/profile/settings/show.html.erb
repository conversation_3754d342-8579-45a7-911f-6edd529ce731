<%= render "agent_portal/profile/tab" %>

<div class="max-w-2xl mt-8 p-6 bg-white rounded-lg shadow-md">
  <h2 class="text-xl mb-6">个人设置</h2>

  <%= form_with(model: @agent, url: agent_portal_profile_setting_path, method: :patch, class: "space-y-6", data: { turbo: false }) do |f| %>
    <% if @agent.errors.any? %>
      <div class="bg-red-50 p-4 rounded-md mb-6">
        <div class="text-red-700">
          <h3 class="text-sm font-medium">请修正以下错误：</h3>
          <ul class="mt-2 list-disc list-inside">
            <% @agent.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>

    <div>
      <%= f.label :name, "帐号", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1 text-sm text-gray-900"><%= @agent.name %></div>
    </div>

    <div>
      <%= f.label :nickname, "昵称", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :nickname, class: "input" %>
    </div>

    <div>
      <%= f.label :channels_count, "管理渠道数", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1 text-sm text-gray-900"><%= @agent.channels.count %> 个</div>
    </div>

    <div>
      <%= f.label :email_address, "邮箱", class: "block text-sm font-medium text-gray-700" %>
      <%= f.email_field :email_address, class: "input" %>
    </div>

    <div>
      <%= f.label :mobile, "手机号", class: "block text-sm font-medium text-gray-700" %>
      <%= f.text_field :mobile, class: "input" %>
    </div>

    <div>
      <%= f.label :timezone, "时区", class: "block text-sm font-medium text-gray-700" %>
      <%= f.select :timezone, options_for_select([
        ["UTC", "UTC"],
        ["Asia/Shanghai", "Asia/Shanghai"],
        ["Asia/Tokyo", "Asia/Tokyo"],
        ["America/New_York", "America/New_York"],
        ["Europe/London", "Europe/London"]
      ], @agent.timezone), {}, { class: "input" } %>
    </div>

    <div class="flex justify-between items-center">
      <div class="text-xs text-gray-500">
        帐号添加时间: <%= @agent.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
      </div>
      <%= f.submit "保存设置", class: "btn btn-primary" %>
    </div>
  <% end %>
</div>
