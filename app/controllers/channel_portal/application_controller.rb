module ChannelPortal
  class ApplicationController < ::ActionController::Base
    include Authentication
    include Pagy::Backend
    layout "channel_portal"

    before_action :check_default_password

    private

    def check_default_password
      return unless Current.channel

      if Current.channel.using_default_password?
        redirect_to channel_portal_force_password_change_path
      end
    end
  end
end
