#!/usr/bin/env ruby

require_relative '../config/environment'

puts "Testing pending message recovery..."

# 创建处理器实例
processor = RedisStreamProcessor.new

# 测试pending消息检查
puts "Testing should_check_pending_messages..."
puts "Initial state: #{processor.send(:should_check_pending_messages?)}"

# 设置上次检查时间为很久以前
processor.instance_variable_set(:@last_pending_check, Time.current - 400)
puts "After setting old timestamp: #{processor.send(:should_check_pending_messages?)}"

# 测试处理pending消息（应该不会有任何pending消息）
puts "\nTesting process_pending_messages..."
begin
  processor.send(:process_pending_messages)
  puts "✅ process_pending_messages completed successfully"
rescue => e
  puts "❌ Error: #{e.message}"
  puts e.backtrace.first(5)
end

puts "\nTest completed!"
