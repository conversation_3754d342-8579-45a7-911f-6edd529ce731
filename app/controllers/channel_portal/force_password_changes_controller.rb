module ChannelPortal
  class ForcePasswordChangesController < ChannelPortal::ApplicationController
    skip_before_action :check_default_password
    layout "blank_channel"

    def show
      @channel = Current.channel
      # 如果不是使用默认密码，重定向到首页
      unless @channel.using_default_password?
        redirect_to channel_portal_root_path
        nil
      end
    end

    def update
      @channel = Current.channel

      # 验证当前密码是否为默认密码
      unless @channel.using_default_password?
        redirect_to channel_portal_root_path, alert: "无效的操作"
        return
      end

      # 验证新密码
      if channel_params[:password].blank?
        @channel.errors.add(:password, "不能为空")
        render :show, status: :unprocessable_entity
        return
      end

      if channel_params[:password] != channel_params[:password_confirmation]
        @channel.errors.add(:password_confirmation, "与新密码不匹配")
        render :show, status: :unprocessable_entity
        return
      end

      # 不能设置为默认密码
      if channel_params[:password] == Channel::DEFAULT_PASSWORD
        @channel.errors.add(:password, "不能设置为默认密码")
        render :show, status: :unprocessable_entity
        return
      end

      # 更新密码
      if @channel.update(password: channel_params[:password])
        redirect_to channel_portal_root_path, notice: "密码修改成功，现在可以正常使用系统了"
      else
        render :show, status: :unprocessable_entity
      end
    end

    private

    def channel_params
      params.require(:channel).permit(:password, :password_confirmation)
    end
  end
end
