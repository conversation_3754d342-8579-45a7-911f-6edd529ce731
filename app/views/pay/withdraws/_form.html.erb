<div class="flex items-center mb-6">
  <%= link_to_back pay_withdraws_path %>
  <h1 class="text-xl"><%= @withdraw.new_record? ? "添加提现订单" : "编辑提现订单" %></h1>
</div>

<%= form_with(model: [:pay, @withdraw], class: "space-y-6", data: { turbo: false }) do |form| %>
  <%= render "shared/error_messages", object: @withdraw %>

  <div class="space-y-4">
    <div>
      <%= form.label :user_id, "用户ID *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :user_id, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :amount, "提现金额 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :amount, class: "input", readonly: !@withdraw.new_record?%>
    </div>
    <div>
      <%= form.label :fee, "费率 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :fee, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :real_amount, "实际金额 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :real_amount, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :order_no, "提现订单号 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :order_no, class: "input", readonly: !@withdraw.new_record? %>
    </div>
    <div>
      <%= form.label :status, "提现状态 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.select :status, options_for_select([
        ['提现成功', 'success'],
        ['拒绝', 'rejected'],
        ['没收', 'confiscated']
      ], @withdraw.status), { prompt: '请选择状态' }, { class: "select select-bordered w-full" } %>
    </div>
    <div>
      <%= form.label :remark, "备注 *", class: "block text-sm font-medium text-gray-700" %>
      <%= form.text_field :remark, class: "input" %>
    </div>
    <div>
  </div>

  </div>

  <div class="flex justify-end gap-4">
    <%= link_to "返回", pay_withdraws_path, class: "btn btn-secondary" %>
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>