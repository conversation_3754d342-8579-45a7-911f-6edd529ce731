# rails redis_stream:processor
namespace :redis_stream do
  desc "Start Redis Stream processor for user operation logs"
  task processor: :environment do
    puts "Starting Redis Stream processor..."
    RedisStreamProcessor.start
  end

  desc "Check and recover pending messages"
  task recover_pending: :environment do
    puts "Checking for pending messages..."
    processor = RedisStreamProcessor.new
    processor.send(:process_pending_messages)
    puts "Pending message recovery completed"
  end

  desc "Show Redis Stream status"
  task status: :environment do
    Rails.cache.redis.with do |redis_client|
      stream_key = RedisStreamProcessor::STREAM_KEY
      group_name = RedisStreamProcessor::GROUP_NAME

      begin
        # Stream信息
        stream_info = redis_client.xinfo(:stream, stream_key)
        puts "Stream: #{stream_key}"
        puts "  Length: #{stream_info['length']}"
        puts "  Groups: #{stream_info['groups']}"

        # Group信息
        group_info = redis_client.xinfo(:groups, stream_key)
        group_info.each do |group|
          puts "Group: #{group['name']}"
          puts "  Consumers: #{group['consumers']}"
          puts "  Pending: #{group['pending']}"
          puts "  Last delivered ID: #{group['last-delivered-id']}"
        end

        # Pending消息信息
        pending_info = redis_client.xpending(stream_key, group_name)
        puts "Pending Messages:"
        puts "  Count: #{pending_info['size']}"
        if pending_info["size"] > 0
          puts "  Min ID: #{pending_info['min_entry_id']}"
          puts "  Max ID: #{pending_info['max_entry_id']}"

          # 显示详细的pending消息
          detailed_pending = redis_client.xpending(stream_key, group_name, "-", "+", 10)
          puts "  Recent pending messages:"
          detailed_pending.each do |msg|
            puts "    ID: #{msg['entry_id']}, Consumer: #{msg['consumer']}, Idle: #{msg['elapsed']}ms, Count: #{msg['count']}"
          end
        end

      rescue Redis::CommandError => e
        puts "Error: #{e.message}"
      end
    end
  end
end
