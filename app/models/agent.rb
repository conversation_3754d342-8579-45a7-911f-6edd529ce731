class Agent < ApplicationRecord
  include TotpAble
  include LoginAble

  has_many :channels, dependent: :destroy
  DEFAULT_PASSWORD = "jaedsy3824ssdR7$2"

  validates :timezone, presence: true
  validates :remark, length: { maximum: 50 }
  # validates :password, length: { in: 8..32, allow_blank: true }

  enum :status, [ :actived, :inactived, :deleted ], default: :actived
  def status_text
    I18n.t("common.status.#{status}")
  end

  # 检查是否使用默认密码
  def using_default_password?
    authenticate(DEFAULT_PASSWORD)
  end
end
