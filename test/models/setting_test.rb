require "test_helper"

class SettingTest < ActiveSupport::TestCase
  def setup
    @setting = Setting.new(
      key: "min_withdraw",
      label: "最小提现",
      value: "50",
      value_type: "integer",
      description: "最小提现",
      created_by: "admin",
      section: "payment"
    )
    # 清理测试数据
    Setting.where(section: [ "test_section", "test_section_2", "other_section" ]).delete_all
    Rails.cache.clear
  end

  def teardown
    # 清理测试数据
    Setting.where(section: [ "test_section", "test_section_2", "other_section" ]).delete_all
    Rails.cache.clear
  end

  test "should be valid" do
    assert @setting.valid?
  end

  test "should require key" do
    @setting.key = nil
    assert_not @setting.valid?
  end

  test "should require label" do
    @setting.label = nil
    assert_not @setting.valid?
  end

  test "should require value_type" do
    @setting.value_type = nil
    assert_not @setting.valid?
  end

  test "should require section" do
    @setting.section = nil
    assert_not @setting.valid?
  end

  test "should have unique key within section" do
    @setting.save
    duplicate = @setting.dup
    assert_not duplicate.valid?
  end

  test "should accept valid value types" do
    Setting::VALUE_TYPES.each do |type|
      @setting.value_type = type
      assert @setting.valid?, "#{type} should be a valid value type"
    end
  end

  test "should reject invalid value types" do
    @setting.value_type = "invalid_type"
    assert_not @setting.valid?
  end

  # 测试 get 方法
  test "get should return default value when setting not found" do
    assert_equal 100, Setting.get("non_existent", section: "payment", default: 100)
  end

  test "get should return typed value" do
    @setting.save
    assert_equal 50, Setting.get("min_withdraw", section: "payment")
  end

  # 测试 set 方法
  test "set should create new setting" do
    assert_difference "Setting.count" do
      Setting.set("new_setting", "value", section: "test")
    end
  end

  test "set should update existing setting" do
    @setting.save
    assert_no_difference "Setting.count" do
      Setting.set("min_withdraw", 100, section: "payment")
    end
    assert_equal 100, Setting.get("min_withdraw", section: "payment")
  end

  test "set should infer value type" do
    Setting.set("integer_setting", 42, section: "test")
    setting = Setting.find_by(key: "integer_setting", section: "test")
    assert_equal "integer", setting.value_type

    Setting.set("boolean_setting", true, section: "test")
    setting = Setting.find_by(key: "boolean_setting", section: "test")
    assert_equal "boolean", setting.value_type
  end

  # 测试 get_section 方法
  test "get_section should return all settings in section" do
    Setting.set("setting1", "value1", section: "test_section")
    Setting.set("setting2", "value2", section: "test_section")
    Setting.set("other_setting", "value3", section: "other_section")

    json_result = Setting.get_section("test_section")
    assert_kind_of String, json_result
    parsed_json = JSON.parse(json_result)
    assert_equal "value1", parsed_json["setting1"]
    assert_equal "value2", parsed_json["setting2"]
    assert_nil parsed_json["other_setting"]
  end

  test "get_section should return json string when json option is true" do
    Setting.set("setting1", "value1", section: "test_section")
    Setting.set("setting2", "value2", section: "test_section")

    json_result = Setting.get_section("test_section", json: true)
    assert_kind_of Hash, json_result
    assert_equal "value1", json_result["setting1"]
    assert_equal "value2", json_result["setting2"]
  end

  test "get_section should use cache" do
    Setting.set("setting1", "value1", section: "test_section")

    # First call should hit database
    assert_difference -> { Setting.where(section: "test_section").count }, 0 do
      Setting.get_section("test_section")
    end

    # Second call should use cache
    assert_difference -> { Setting.where(section: "test_section").count }, 0 do
      Setting.get_section("test_section")
    end
  end

  test "get_section should handle invalid json in cache" do
    # 使用唯一的缓存键避免并行测试冲突
    unique_section = "test_section_#{SecureRandom.hex(8)}"
    cache_key = "setting:section:#{unique_section}"

    # 确保缓存是空的
    Rails.cache.delete(cache_key)

    # 直接写入无效的 JSON 到缓存
    Rails.cache.write(cache_key, "invalid json", raw: true)

    # 应该返回空哈希（方法内部会处理无效 JSON）
    result = Setting.get_section(unique_section, json: true)
    assert_equal({}, result)

    # 清理测试缓存
    Rails.cache.delete(cache_key)
  end

  # 测试类型转换
  test "typed_value should convert integer" do
    @setting.value = "42"
    @setting.value_type = "integer"
    assert_equal 42, @setting.typed_value
  end

  test "typed_value should convert float" do
    @setting.value = "3.14"
    @setting.value_type = "float"
    assert_equal 3.14, @setting.typed_value
  end

  test "typed_value should convert boolean" do
    @setting.value = "true"
    @setting.value_type = "boolean"
    assert @setting.typed_value

    @setting.value = "false"
    assert_not @setting.typed_value
  end

  test "typed_value should convert json" do
    @setting.value = '{"key": "value"}'
    @setting.value_type = "json"
    assert_equal({ "key" => "value" }, @setting.typed_value)
  end

  test "typed_value should convert array" do
    @setting.value = "a,b,c"
    @setting.value_type = "array"
    assert_equal [ "a", "b", "c" ], @setting.typed_value
  end

  # 测试 sections 方法
  test "sections should return all distinct sections sorted" do
    # 获取当前的sections数量
    initial_sections = Setting.sections

    # 创建不同分组的设置
    Setting.create!(key: "test_key1", label: "Label 1", value: "value1", value_type: "string", section: "test_payment")
    Setting.create!(key: "test_key2", label: "Label 2", value: "value2", value_type: "string", section: "test_system")
    Setting.create!(key: "test_key3", label: "Label 3", value: "value3", value_type: "string", section: "test_payment")
    Setting.create!(key: "test_key4", label: "Label 4", value: "value4", value_type: "string", section: "test_auth")

    # 清除缓存以确保获取最新数据
    Rails.cache.delete("setting:sections")

    sections = Setting.sections
    # 验证新创建的sections都包含在结果中
    assert_includes sections, "test_auth"
    assert_includes sections, "test_payment"
    assert_includes sections, "test_system"
    # 验证结果是排序的
    assert_equal sections, sections.sort
    # 验证没有重复
    assert_equal sections, sections.uniq
  end

  test "sections should return empty array when no settings exist" do
    Setting.delete_all
    assert_equal [], Setting.sections
  end
end
