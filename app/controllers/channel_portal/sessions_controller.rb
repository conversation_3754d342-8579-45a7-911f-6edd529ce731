class ChannelPortal::Sessions<PERSON>ontroller < ChannelPortal::ApplicationController
  allow_unauthenticated_access
  skip_before_action :check_default_password
  rate_limit to: 10, within: 3.minutes, only: :create, with: -> { redirect_to new_channel_portal_session_path, alert: "Try again later." }

  layout "blank_channel"

  # GET /channel_portal/sessions/new
  def new
  end

  # POST /channel_portal/sessions
  def create
    channel = Channel.find_by(name: params[:email_address])
    if channel && channel.authenticate(params[:password])
      unless channel.actived?
        redirect_to new_channel_portal_session_path, alert: "您的帐号已被锁定，请联系管理员。"
        return
      end

      start_new_session_for channel
      SystemLog.log(
        actor: channel,
        loggable: channel,
        operate: :channel_login,
        action: "渠道商登录",
        snapshot: { name: channel.name }
      )

      # 检查是否使用默认密码，如果是则重定向到强制修改密码页面
      if channel.using_default_password?
        redirect_to channel_portal_force_password_change_path
      else
        redirect_url = after_authentication_url
        if redirect_url.include?(new_channel_portal_session_path)
          redirect_to channel_portal_root_path
        else
          redirect_to redirect_url
        end
      end
    else
      redirect_to new_channel_portal_session_path, alert: "帐号或密码不正确，请重新输入。"
    end
  end

  # DELETE /channel_portal/sessions
  def destroy
    if Current.channel
      SystemLog.log(
        actor: Current.channel,
        loggable: Current.channel,
        operate: :logout,
        action: "渠道商登出",
        snapshot: { name: Current.channel.name }
      )
    end
    terminate_session
    redirect_to new_channel_portal_session_path
  end
end
