require "test_helper"

class Campaign::ChannelsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @channel = channels(:active_channel)
    @admin = admins(:one)
    sign_in_as @admin
  end

  test "should get index" do
    get campaign_channels_url
    assert_response :success
  end

  test "should get new" do
    get new_campaign_channel_url
    assert_response :success
  end

  test "should create channel" do
    assert_difference("Channel.count") do
      post campaign_channels_url, params: {
        channel: {
          name: "TestChannel",
          nickname: "Test Nickname",
          email_address: "<EMAIL>",
          mobile: "1234567890",
          remark: "Test Remark",
          status: "actived",
          password: "password123",
          agent_id: agents(:active_agent).id,
          promotion_type_id: promotion_types(:facebook).id
        }
      }
    end

    assert_redirected_to campaign_channel_url(Channel.last)
    assert_equal "渠道 TestChannel 添加成功", flash[:notice]
  end

  test "should show channel" do
    get campaign_channel_url(@channel)
    assert_response :success
  end

  test "should get edit" do
    get edit_campaign_channel_url(@channel)
    assert_response :success
  end

  test "should update channel" do
    patch campaign_channel_url(@channel), params: {
      channel: {
        name: "UpdatedChannel",
        nickname: "Updated Nickname",
        email_address: "<EMAIL>",
        mobile: "0987654321",
        remark: "Updated Remark",
        status: "actived",
        promotion_type_id: promotion_types(:facebook).id
      }
    }

    assert_redirected_to campaign_channels_url
    assert_equal "渠道 UpdatedChannel 修改成功", flash[:notice]
  end

  test "should destroy channel" do
    assert_difference("Channel.where.not(status: :deleted).count", -1) do
      delete campaign_channel_url(@channel)
    end

    assert_redirected_to campaign_channels_url
    assert_equal "删除成功", flash[:notice]
  end

  test "should search channels" do
    get campaign_channels_url, params: { search: @channel.name }
    assert_response :success
  end

  test "should filter channels by status" do
    get campaign_channels_url, params: { status: "actived" }
    assert_response :success
  end

  test "should reset channel password" do
    post reset_password_campaign_channel_url(@channel)
    assert_response :success
  end

  test "should not reset password with short password" do
    post reset_password_campaign_channel_url(@channel), params: { password: "short" }
    assert_response :success
    assert_equal "密码长度不能少于8位", flash[:alert]
  end
end
