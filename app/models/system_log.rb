class SystemLog < ApplicationRecord
  belongs_to :actor, polymorphic: true
  belongs_to :loggable, polymorphic: true

  validates :operate, presence: true
  validates :actor, presence: true
  validates :loggable, presence: true

  enum :operate, {
    admin_login: 0,      # admin 登录
    agent_login: 1,      # 代理商登录
    channel_login: 2,    # 渠道登录
    admin_logout: 20,     # admin 登出
    agent_logout: 21,     # 代理商登出
    channel_logout: 22,   # 渠道登出
    create_admin: 40,     # 创建管理员
    update_admin: 41,     # 更新管理员
    destroy_admin: 42,    # 更新管理员
    reset_admin_password: 43,     # 重置管理员密码
    create_role: 60,      # 创建角色
    update_role: 61,      # 更新角色
    destroy_role: 62,     # 删除角色
    create_permission: 80,     # 创建权限
    update_permission: 81,     # 更新权限
    destroy_permission: 82,    # 删除权限
    create_game: 100,      # 添加游戏
    update_game: 101,      # 更新游戏
    destroy_game: 102,     # 删除游戏
    create_agent: 120,     # 创建代理商
    update_agent: 121,     # 更新代理商
    reset_agent_password: 122,     # 重置代理商密码
    destroy_agent: 123,    # 删除代理商
    create_channel: 140,   # 创建渠道
    update_channel: 141,   # 更新渠道
    destroy_channel: 142,  # 删除渠道
    reset_channel_password: 143,  # 重置渠道密码
    create_promotion_type: 150,   # 创建推广类型
    update_promotion_type: 151,   # 更新推广类型
    enable_promotion_type: 152,  # 启用推广类型
    disable_promotion_type: 153,  # 禁用推广类型
    destroy_promotion_type: 154,  # 删除推广类型
    create_payment: 160,   # 创建支付
    update_payment: 161,   # 更新支付
    destroy_payment: 162,  # 删除支付
    user_register: 180,    # 用户注册
    create_setting: 300,     # 创建设置
    update_setting: 301,     # 更新设置
    enable_setting: 302,     # 启用设置
    disable_setting: 303,    # 禁用设置
    destroy_setting: 304,    # 删除设置
    other: 999             # 其他
  }

  def self.log(actor:, loggable:, operate:, action: nil, snapshot: nil)
    create!(
      actor: actor,
      loggable: loggable,
      operate: operate,
      action: action,
      snapshot: snapshot,
      ip: Current.request&.remote_ip,
      user_agent: Current.request&.user_agent
    )
  end
end
