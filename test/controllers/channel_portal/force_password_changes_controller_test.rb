require "test_helper"

class ChannelPortal::ForcePasswordChangesControllerTest < ActionDispatch::IntegrationTest
  def setup
    @default_channel = channels(:default_password_channel)
    @active_channel = channels(:active_channel)
  end

  test "channel model has using_default_password method" do
    assert @default_channel.respond_to?(:using_default_password?)
    assert @default_channel.using_default_password?
    assert_not @active_channel.using_default_password?
  end

  test "should show force password change page when using default password" do
    sign_in_as @default_channel, Channel::DEFAULT_PASSWORD

    # 访问强制修改密码页面
    get channel_portal_force_password_change_path
    assert_response :success
    assert_select "h2", "安全提醒"
  end

  test "should redirect to home if not using default password" do
    sign_in_as @active_channel

    # 尝试访问强制修改密码页面应该重定向到首页
    get channel_portal_force_password_change_path
    assert_redirected_to channel_portal_root_path
  end

  test "should update password successfully" do
    # 模拟默认密码channel登录
    sign_in_as @default_channel, Channel::DEFAULT_PASSWORD

    # 修改密码
    patch channel_portal_force_password_change_path, params: {
      channel: {
        password: "new_secure_password123",
        password_confirmation: "new_secure_password123"
      }
    }

    assert_redirected_to channel_portal_root_path
    assert_equal "密码修改成功，现在可以正常使用系统了", flash[:notice]

    # 验证密码已更改
    @default_channel.reload
    assert @default_channel.authenticate("new_secure_password123")
    assert_not @default_channel.using_default_password?
  end

  test "should not allow setting default password as new password" do
    # 模拟默认密码channel登录
    sign_in_as @default_channel, Channel::DEFAULT_PASSWORD

    # 尝试设置默认密码作为新密码
    patch channel_portal_force_password_change_path, params: {
      channel: {
        password: Channel::DEFAULT_PASSWORD,
        password_confirmation: Channel::DEFAULT_PASSWORD
      }
    }

    assert_response :unprocessable_entity
    # 检查是否有错误信息（不依赖特定的CSS类）
    assert_match /不能设置为默认密码/, response.body
  end
end
