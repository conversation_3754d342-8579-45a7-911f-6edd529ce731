class RedisStreamProcessor
  STREAM_KEY = "game_bet_stream".freeze
  GROUP_NAME = "game_bet_stream_group".freeze
  CONSUMER_NAME = "game_bet_stream_consumer".freeze
  BATCH_SIZE = 100
  PENDING_CHECK_INTERVAL = 300 # 5分钟检查一次pending消息
  PENDING_TIMEOUT = 60 # 1分钟超时

  def self.start
    new.start
  end

  def initialize
    @last_pending_check = nil
    @running = true
  end

  def start
    ensure_consumer_group_exists
    process_stream
  end

  def stop
    @running = false
    Rails.logger.info "RedisStreamProcessor stopping..."
  end

  private

  def ensure_consumer_group_exists
    Rails.cache.redis.with do |redis_client|
      redis_client.xgroup(:create, STREAM_KEY, GROUP_NAME, "0", mkstream: true)
    end
  rescue Redis::CommandError => e
    # Group might already exist, which is fine
    Rails.logger.info "Consumer group might already exist: #{e.message}"
  end

  def process_stream
    while @running
      # 定期检查pending消息
      check_pending_messages_if_needed

      # puts "#{Time.now} Processing stream..."
      Rails.cache.redis.with do |redis_client|
        begin
          messages = redis_client.xreadgroup(
            GROUP_NAME,
            CONSUMER_NAME,
            STREAM_KEY,
            ">",
            count: BATCH_SIZE,
            block: 500,
            noack: true
          )

          if messages.empty?
            next
          end

          bets_to_process = []
          entry_ids = []
          messages.each do |stream, entries|
            puts "entries count: #{entries.count}"
            entries.each do |entry_id, fields|
              entry_ids << entry_id
              bets_to_process << {
                entry_id: entry_id,
                user_id: fields["user_id"].to_i,
                bet: fields["bet"].to_i,
                real_win: fields["real_win"].to_i,
                win: fields["win"].to_i,
                game_number: fields["game_number"].to_s,
                round_id: fields["round_id"].to_s,
                before_money: fields["before_money"].to_i,
                after_money: fields["after_money"].to_i,
                action_time: fields["bet_time"].to_i
              }
            end
          end

          unless bets_to_process.empty?
            # 批量写入DynamoDB
            result = DynamodbLogger.batch_log_bet(bets_to_process)

            # 只确认成功写入的消息
            if result[:success]
              acknowledge_messages(redis_client, entry_ids)
            else
              # 只确认成功写入的消息
              successful_entry_ids = entry_ids.select.with_index do |_, index|
                result[:processed].include?(bets_to_process[index])
              end
              acknowledge_messages(redis_client, successful_entry_ids)

              # 记录失败的消息
              failed_entry_ids = entry_ids.select.with_index do |_, index|
                result[:failed].include?(bets_to_process[index])
              end
              puts "Failed to process #{failed_entry_ids.size} messages: #{failed_entry_ids.join(', ')}"
            end
          end
        rescue => e
          Rails.logger.error "Error processing stream: #{e.message}"
          sleep 5 # Wait before retrying
        end
      end
    end
    Rails.logger.info "RedisStreamProcessor stopped"
  end

  def process_entry(entry_id, fields)
    # 这个方法保留但不再使用，因为我们现在使用批量处理
    user_id = fields["user_id"].to_i
    action_time = fields["bet_time"].to_i

    DynamodbLogger.log_bet(
      user_id: user_id,
      bet: fields["bet"].to_f,
      real_win: fields["real_win"],
      win: fields["win"],
      game_number: fields["game_number"],
      round_id: fields["round_id"],
      before_money: fields["before_money"],
      after_money: fields["after_money"],
      action_time: action_time
    )
  end

  def acknowledge_messages(redis_client, entry_ids)
    return if entry_ids.empty?
    redis_client.xack(STREAM_KEY, GROUP_NAME, *entry_ids)
  end

  # 保留单个确认方法以备不时之需
  def acknowledge_message(redis_client, stream, entry_id)
    redis_client.xack(STREAM_KEY, GROUP_NAME, entry_id)
  end

  # 检查是否需要处理pending消息
  def check_pending_messages_if_needed
    return unless should_check_pending_messages?

    @last_pending_check = Time.current
    process_pending_messages
  rescue => e
    Rails.logger.error "Failed to check pending messages: #{e.message}"
  end

  # 判断是否应该检查pending消息
  def should_check_pending_messages?
    @last_pending_check.nil? || (Time.current - @last_pending_check) > PENDING_CHECK_INTERVAL
  end

  # 处理pending消息
  def process_pending_messages
    Rails.cache.redis.with do |redis_client|
      begin
        # 获取所有pending的消息
        pending_messages = redis_client.xpending(
          STREAM_KEY,
          GROUP_NAME,
          "-", # 最小ID
          "+", # 最大ID
          100  # 返回数量
        )

        if pending_messages.empty?
          Rails.logger.debug "No pending messages found"
          return
        end

        Rails.logger.info "Found #{pending_messages.size} pending messages"
        recovered_count = 0

        pending_messages.each do |message|
          entry_id = message["entry_id"]
          consumer = message["consumer"]
          idle_time = message["elapsed"]
          delivery_count = message["count"]

          # 如果消息空闲时间超过设定的超时时间
          if idle_time > PENDING_TIMEOUT * 1000 # Redis返回的是毫秒
            Rails.logger.info "Processing timeout message: Entry ID: #{entry_id}, idle time: #{idle_time / 1000.0}s, consumer: #{consumer}"

            begin
              # 获取消息详情
              message_details = redis_client.xrange(STREAM_KEY, entry_id, entry_id).first
              if message_details
                # 重新处理消息
                process_pending_message(entry_id, message_details[1])
                # 确认消息
                redis_client.xack(STREAM_KEY, GROUP_NAME, entry_id)
                recovered_count += 1
                Rails.logger.info "Message #{entry_id} recovered and acknowledged"
              else
                Rails.logger.warn "Message details not found for entry_id: #{entry_id}"
              end
            rescue => e
              Rails.logger.error "Failed to process pending message #{entry_id}: #{e.message}"
            end
          end
        end

        Rails.logger.info "Recovered #{recovered_count} pending messages" if recovered_count > 0
      rescue Redis::CommandError => e
        if e.message.include?("NOGROUP") || e.message.include?("No such key")
          Rails.logger.debug "Stream or Consumer Group does not exist: #{e.message}"
        else
          raise e
        end
      end
    end
  end

  # 处理单个pending消息
  def process_pending_message(entry_id, fields)
    bet_data = {
      user_id: fields["user_id"].to_i,
      bet: fields["bet"].to_i,
      real_win: fields["real_win"].to_i,
      win: fields["win"].to_i,
      game_number: fields["game_number"].to_s,
      round_id: fields["round_id"].to_s,
      before_money: fields["before_money"].to_i,
      after_money: fields["after_money"].to_i,
      action_time: fields["bet_time"].to_i
    }

    # 使用单条记录写入，避免批量处理的复杂性
    DynamodbLogger.log_bet(**bet_data)
    Rails.logger.debug "Pending message processed: Entry ID: #{entry_id}"
  end
end
